package com.superhexa.supervision

import android.content.Context
import android.content.Intent
import android.database.ContentObserver
import android.net.Uri
import android.os.Bundle
import android.os.LocaleList
import androidx.fragment.app.proxyFragmentFactory
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.navigation.fragment.NavHostFragment
import androidx.window.layout.WindowInfoTracker
import com.github.fragivity.applySlideInOut
import com.github.fragivity.findOrCreateNavHostFragment
import com.github.fragivity.loadRoot
import com.github.fragivity.navigator
import com.github.fragivity.push
import com.superhexa.supervision.WindowFeatureHelper.isFoldingFeatureSupport
import com.superhexa.supervision.WindowFeatureHelper.onLayoutInfoUpdate
import com.superhexa.supervision.app.presentation.FragmentLifeCallBackEx
import com.superhexa.supervision.app.presentation.FragmentLifecycleObserverEx
import com.superhexa.supervision.app.presentation.splash.SplashFragment
import com.superhexa.supervision.deeplink.SchemeJumpUtil
import com.superhexa.supervision.feature.home.startUp.HomeInitalizer
import com.superhexa.supervision.feature.videoeditor.presentation.selector.services.DownlaodBroadcastUtil
import com.superhexa.supervision.feature.xiaoai.repository.InhibitionNotificationRepository
import com.superhexa.supervision.feature.xiaoai.service.MiLiteComponent
import com.superhexa.supervision.feature.xiaoai.service.MiLiteHelper.GLASSES_WAKE_INHIBITION
import com.superhexa.supervision.feature.xiaoai.service.MiLiteHelper.GLASSES_WAKE_INHIBITION_VALUE
import com.superhexa.supervision.library.base.basecommon.arouter.ARouterTools
import com.superhexa.supervision.library.base.basecommon.arouter.RouterKey
import com.superhexa.supervision.library.base.basecommon.arouter.impl
import com.superhexa.supervision.library.base.basecommon.commonbean.BinderWrapperBean
import com.superhexa.supervision.library.base.basecommon.config.BundleKey
import com.superhexa.supervision.library.base.basecommon.config.BundleKey.QuickLinkData
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.credential.AccountManager
import com.superhexa.supervision.library.base.basecommon.extension.printDetail
import com.superhexa.supervision.library.base.basecommon.extension.putBinder
import com.superhexa.supervision.library.base.basecommon.tools.AppEnvironment
import com.superhexa.supervision.library.base.basecommon.tools.AppUtils
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.data.model.QuickLink
import com.superhexa.supervision.library.base.debugprofile.showDebugView
import com.superhexa.supervision.library.base.presentation.activity.BaseActivity
import com.superhexa.supervision.library.base.superhexainterfaces.login.ILoginModuleApi
import com.superhexa.supervision.library.crash.upgrade.UpgradeManager
import com.superhexa.supervision.library.db.DbHelper
import com.superhexa.supervision.library.db.StartTranslateEvent
import com.superhexa.supervision.library.db.bean.MediaBean
import com.superhexa.supervision.library.mipush.MiPushInteractor
import com.superhexa.supervision.library.statistic.StatisticHelper
import com.superhexa.supervision.library.statistic.annotations.AnnotationUtil
import com.superhexa.supervision.library.statistic.constants.EventCons
import com.superhexa.supervision.library.statistic.constants.PropertyKeyCons
import io.objectbox.query.QueryBuilder
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.kodein.di.generic.instance
import timber.log.Timber
import java.util.Locale

class NavHostActivity : BaseActivity(R.layout.activity_nav_host) {
    private lateinit var navHostFragment: NavHostFragment
    private val pushInteractor by instance<MiPushInteractor>()
    private val appEnvironment by instance<AppEnvironment>()
    private val isPageLoad = MutableLiveData(false)

    override fun attachBaseContext(base: Context) {
        val configuration = base.resources.configuration
        configuration.setLocale(Locale.SIMPLIFIED_CHINESE)
        val localeList = LocaleList(Locale.SIMPLIFIED_CHINESE)
        configuration.setLocales(localeList)
        LocaleList.setDefault(localeList)
        super.attachBaseContext(base.createConfigurationContext(configuration))
    }

    private lateinit var accountObserver: ContentObserver

    override fun onCreate(savedInstanceState: Bundle?) {
        proxyFragmentFactory()
        super.onCreate(savedInstanceState)
        navHostFragment = findOrCreateNavHostFragment(R.id.navHost)
        // 注册和观察navHostFragment 容器中所有运行的fragment的生命周期
        FragmentLifecycleObserverEx(
            this,
            navHostFragment.childFragmentManager,
            FragmentLifeCallBackEx()
        )
        // 打印backstack 堆栈 方便调试
        if (BuildConfig.DEBUG) {
            navHostFragment.showDebugView(this)
        }
        navHostFragment.loadRoot(SplashFragment::class)
        Timber.d("nav host activity onCreate")
        DownlaodBroadcastUtil.startScreenBroadcastReceiver(this)
        try {
            if (savedInstanceState?.containsKey("nav_state") == true) {
                navHostFragment.navController.restoreState(savedInstanceState.getBundle("nav_state"))
            }
        } catch (e: Exception) {
            Timber.d("restoreState--error=${e.printDetail()}")
        }

        handleIntent(intent)
        addWindowInfoTracker()
        MiLiteComponent.addMainActivityLifecycleObserver(this, isPageLoad)
        EventBus.getDefault().register(this)
    }

    /**
     * 监听折叠屏设备的屏幕信息.
     */
    private fun addWindowInfoTracker() {
        if (isFoldingFeatureSupport(this)) {
            lifecycleScope.launch(Dispatchers.Main) {
                lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                    WindowInfoTracker.getOrCreate(this@NavHostActivity)
                        .windowLayoutInfo(this@NavHostActivity)
                        .collectLatest { newLayoutInfo ->
                            onLayoutInfoUpdate(newLayoutInfo)
                        }
                }
            }
        } else {
            Timber.w("it is not support folding feature.")
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    override fun onResume() {
        super.onResume()
        switchMIUIDialog(0)
        ILoginModuleApi::class.java.impl.checkLoginState()
    }

    override fun onPause() {
        super.onPause()
        switchMIUIDialog(1)
    }

    private fun switchMIUIDialog(value: Int) {
        val miui = appEnvironment.isMIUI()
        Timber.d("switchMIUIDialog----value=$value,miui:$miui")
        if (!miui) {
            Timber.d("switchMIUIDialog is not miui abort")
            return
        }
        try {
            val resolver = LibBaseApplication.instance.contentResolver
            val uri = Uri.parse("content://com.android.bluetooth.ble.app.confignet.provider")
            val values = Bundle()
            values.putInt("value", value)
            // 尝试进行调用
            val result = resolver.call(uri, "", "", values)
            if (result != null) {
                Timber.d("NavHostActivity----Provider call successful")
            } else {
                Timber.e("NavHostActivity----Provider returned null result")
            }
        } catch (e: IllegalArgumentException) {
            Timber.e("NavHostActivity----Unknown authority or invalid provider, e: ${e.printDetail()}")
            // 如果是找不到 Provider 或其他异常，返回 false 或者执行后备逻辑
        } catch (e: SecurityException) {
            Timber.e("NavHostActivity----Permission denied for ContentProvider, e: ${e.printDetail()}")
            // 处理权限异常
        } catch (e: Exception) {
            Timber.e("NavHostActivity----Unexpected error occurred, e: ${e.printDetail()}")
            // 捕获任何其他意外错误
        }
    }

    private fun handleIntent(intent: Intent?) {
        kotlin.runCatching {
            if (intent == null) return
            if (intent.getIntExtra(GLASSES_WAKE_INHIBITION, 0) == GLASSES_WAKE_INHIBITION_VALUE) {
                InhibitionNotificationRepository.cancelInhibitionNotification(this)
            }

            // 处理转写通知跳转
            if (handleTranscriptionNotificationIntent(intent)) {
                return
            }

            val data = intent.data ?: Uri.parse(pushInteractor.getMessage(intent) ?: "")
            val scheme = data.scheme
            val host = data.host
            if ("https" == scheme && "hlth.io.mi.com" == host) {
                Timber.d("onReceivedhandleIntent--path=${data.path}")
                dealHlthLinkData(data)
            } else if (data != null && AccountManager.isSignedIn()) {
                SchemeJumpUtil.parseUriAndJump(navHostFragment, data)
            }
        }.getOrElse {
            Timber.d("handleIntent--error=${it.printDetail()}")
        }
    }

    /**
     * 处理转写通知的跳转Intent
     * @param intent Intent
     * @return Boolean 是否处理了转写通知跳转
     */
    private fun handleTranscriptionNotificationIntent(intent: Intent): Boolean {
        val filePath = intent.getStringExtra("transcription_file_path")

        if (filePath != null) {
            val mediaId = intent.getIntExtra("transcription_media_id", -1)
            val userId = intent.getStringExtra("transcription_user_id")
            val initialTab = intent.getStringExtra("transcription_initial_tab")

            Timber.i("处理转写通知跳转: filePath=$filePath, mediaId=$mediaId, userId=$userId, initialTab=$initialTab")

            // 在后台线程中查找MediaBean并跳转
            lifecycleScope.launch {
                try {
                    val mediaBean = if (mediaId != -1 && userId != null) {
                        // 如果有完整的MediaBean信息，直接查找
                        findMediaBeanForTranscription(mediaId, filePath, userId)
                    } else {
                        // 如果没有完整信息，尝试根据文件路径查找
                        findMediaBeanByFilePath(filePath)
                    }

                    if (mediaBean != null) {
                        // 在主线程中执行跳转
                        withContext(Dispatchers.Main) {
                            navigateToTranscriptionFragment(mediaBean, initialTab)
                        }
                    } else {
                        Timber.w("未找到对应的MediaBean进行跳转: $filePath")
                    }
                } catch (e: Exception) {
                    Timber.e(e, "处理转写通知跳转失败")
                }
            }
            return true
        }
        return false
    }

    /**
     * 查找用于转写的MediaBean
     */
    private suspend fun findMediaBeanForTranscription(mediaId: Int, filePath: String, userId: String): MediaBean? {
        return withContext(Dispatchers.IO) {
            try {
                val mediaBoxFor = DbHelper.getBoxStore().boxFor(MediaBean::class.java)
                mediaBoxFor.query()
                    .equal(com.superhexa.supervision.library.db.bean.MediaBean_.id, mediaId.toLong())
                    .and()
                    .equal(com.superhexa.supervision.library.db.bean.MediaBean_.path, filePath, QueryBuilder.StringOrder.CASE_SENSITIVE)
                    .and()
                    .equal(com.superhexa.supervision.library.db.bean.MediaBean_.useId, userId, QueryBuilder.StringOrder.CASE_SENSITIVE)
                    .build()
                    .findFirst()
            } catch (e: Exception) {
                Timber.e(e, "查找MediaBean失败: id=$mediaId, path=$filePath, userId=$userId")
                null
            }
        }
    }

    /**
     * 根据文件路径查找MediaBean
     */
    private suspend fun findMediaBeanByFilePath(filePath: String): MediaBean? {
        return withContext(Dispatchers.IO) {
            try {
                val mediaBoxFor = DbHelper.getBoxStore().boxFor(MediaBean::class.java)
                mediaBoxFor.query()
                    .equal(com.superhexa.supervision.library.db.bean.MediaBean_.path, filePath, QueryBuilder.StringOrder.CASE_SENSITIVE)
                    .build()
                    .findFirst()
            } catch (e: Exception) {
                Timber.e(e, "根据文件路径查找MediaBean失败: path=$filePath")
                null
            }
        }
    }

    /**
     * 跳转到转写Fragment
     */
    private fun navigateToTranscriptionFragment(mediaBean: MediaBean, initialTab: String? = null) {
        try {
            val bundle = Bundle()
            bundle.putBinder(BundleKey.Record, BinderWrapperBean(mediaBean))

            // 添加初始Tab参数
            if (initialTab != null) {
                bundle.putString("initial_tab", initialTab)
            }

            navHostFragment.navigator.push(
                ARouterTools.navigateToFragment(RouterKey.MIWEAR_RECORD_TRANSCRIPTION_FRAGMENT)::class
            ) {
                arguments = bundle
                applySlideInOut()
            }

            Timber.i("成功跳转到转写界面: ${mediaBean.fileName}, initialTab=$initialTab")
        } catch (e: Exception) {
            Timber.e(e, "跳转到转写界面失败")
        }
    }

    private fun dealHlthLinkData(data: Uri) {
        when (data.path) {
            "/download" -> {
                val redir = data.getQueryParameter("redir")
                val name = data.getQueryParameter("name")
                val mac = data.getQueryParameter("mac")
                if (redir?.isNotEmpty() == true && mac?.isNotEmpty() == true) {
                    MMKVUtils.encode(QuickLinkData, QuickLink(mac, redir))
                }
                Timber.d("onReceivedQuickLink--redir=$redir,mac=$mac,name=$name")
            }

            "/deeplink" -> {
                val linkpage = data.getQueryParameter("linkpage")
                when (linkpage) {
                    "miwear_media" -> navigateToMiWearFileSpace()
                }
                Timber.d("onReceivedDeepLink--linkpage=$linkpage")
            }
        }
    }

    // 跳转O95文件空间
    private fun navigateToMiWearFileSpace() {
        navHostFragment.navigator.push(
            ARouterTools.navigateToFragment(RouterKey.miwearglasses_MiWearFileSpaceFragment)::class
        ) {
            applySlideInOut()
        }
    }

    // 跳转O95实时翻译
    private fun navigateToTranslate(enter: Boolean) {
        val bundle = Bundle()
        bundle.putBinder(BundleKey.EnterTranslation, BinderWrapperBean(enter))
        navHostFragment.navigator.push(
            ARouterTools.navigateToFragment(RouterKey.MIWEAR_TRANSLATE_HUB_FRAGMENT)::class
        ) {
            arguments = bundle
            applySlideInOut()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: StartTranslateEvent) {
        Timber.i("onEvent StartTranslateEvent $event")
        navigateToTranslate(event.isEnter)
    }

    override fun onDestroy() {
        UpgradeManager.clearRes()
        DownlaodBroadcastUtil.unRegisteScreenBroadcast(this)
        HomeInitalizer.onExitApp(this)
        EventBus.getDefault().unregister(this)
        appQuitStatistic("onDestroy")
//        DeviceDecoratorFactory.clearDecorator()
        super.onDestroy()
    }

    fun onFragmentPageLoad() {
        Timber.d("onFragmentPageLoad")
        isPageLoad.value = true
    }

    private fun appQuitStatistic(lifecycleMethod: String) {
        val pageName = AnnotationUtil.fetchScreenName(supportFragmentManager)
        Timber.e("app 退出 %s pageName : %s", lifecycleMethod, pageName)
        StatisticHelper
            .addEventProperty(PropertyKeyCons.Property_EVENT_DURATION, AppUtils.getDuration())
            .addEventProperty(PropertyKeyCons.Property_SCREEN_NAME, pageName)
            .doEvent(EventCons.EventKey_SV1_APP_END)
        AppUtils.resetDuration()
        MMKVUtils.removeKey(QuickLinkData)
    }

    /**
     * 崩溃后, Activity清空缓存,因为依附于 Activity的Fragment会出问题
     * 此方法禁止fragment的状态管理的原始实现，bug SW-191 视频对比fragment奔溃后，容易导致下层的fragment直接显示
     *
     * @param outState
     */

    override fun onSaveInstanceState(savedInstanceState: Bundle) {
        super.onSaveInstanceState(savedInstanceState)
        try {
            savedInstanceState.putBundle("nav_state", navHostFragment.navController.saveState())
        } catch (e: Exception) {
            Timber.d("onSaveInstanceState--error=${e.printDetail()}")
        }
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        try {
            navHostFragment.navController.restoreState(savedInstanceState.getBundle("nav_state"))
        } catch (e: Exception) {
            Timber.d("onRestoreInstanceState--error=${e.printDetail()}")
        }
    }
}
