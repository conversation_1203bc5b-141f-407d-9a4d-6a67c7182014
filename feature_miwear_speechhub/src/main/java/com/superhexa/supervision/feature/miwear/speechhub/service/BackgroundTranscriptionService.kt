@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>","TooGenericExceptionCaught", "MaxLineLength")

package com.superhexa.supervision.feature.miwear.speechhub.service

import com.superhexa.supervision.feature.miwear.speechhub.data.repository.SummaryDataRepository
import com.superhexa.supervision.feature.miwear.speechhub.data.retrofit.service.SummaryRetrofitFactory
import com.superhexa.supervision.feature.miwear.speechhub.data.retrofit.service.SummaryRetrofitService
import com.superhexa.supervision.feature.miwear.speechhub.utils.TranscriptionPollingManager
import com.superhexa.supervision.library.db.AudioTranscriptionDbHelper
import com.superhexa.supervision.library.db.bean.MediaBean
import com.xiaomi.ai.capability.request.Phrase
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlin.coroutines.resume
import timber.log.Timber
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 后台转写服务管理器
 * 使用Application级别的协程作用域，管理后台转写轮询任务
 * 支持监听者注册机制，允许UI组件监听转写状态变化
 */
class BackgroundTranscriptionService private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: BackgroundTranscriptionService? = null

        // 总结相关常量
        private const val POLLING_DELAY_MS = 3000L // 3秒轮询间隔
        private const val MAX_RETRY_ATTEMPTS = 20 // 最大重试次数
        private const val REQUEST_SUMMARY_ERROR = 10001
        private const val GET_SUMMARY_RESULT_ERROR = 10002

        fun getInstance(): BackgroundTranscriptionService {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: BackgroundTranscriptionService().also { INSTANCE = it }
            }
        }
    }

    // 使用Application级别的协程作用域，不依赖任何UI组件的生命周期
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 转写轮询管理器
    private val pollingManager = TranscriptionPollingManager(serviceScope)

    // 总结Repository
    private val summaryRepository = SummaryDataRepository(
        SummaryRetrofitFactory.provideService(SummaryRetrofitService::class.java)
    )

    // 监听者列表，使用线程安全的集合
    private val listeners = CopyOnWriteArrayList<TranscriptionStateListener>()

    // 当前正在进行的转写任务，key为taskId，value为文件路径
    private val activeTranscriptions = ConcurrentHashMap<String, String>()

    // 转写上下文信息，用于数据库更新
    private val transcriptionContexts = ConcurrentHashMap<String, TranscriptionContext>()

    // 当前正在进行的总结任务，key为summaryTaskId，value为转写taskId
    private val activeSummaries = ConcurrentHashMap<String, String>()

    // 总结上下文信息，用于数据库更新和轮询
    private val summaryContexts = ConcurrentHashMap<String, SummaryContext>()

    /**
     * 转写上下文数据类，包含数据库更新所需的所有信息
     */
    private data class TranscriptionContext(
        val taskId: String,
        val filePath: String,
        val mediaBean: MediaBean?,
        val summaryTemplate: String,
        val distinguishSpeakers: Boolean,
        val isReTranscribe: Boolean,
        val language: String
    )

    /**
     * 总结上下文数据类，包含总结轮询所需的所有信息
     */
    private data class SummaryContext(
        val transcriptionTaskId: String,
        val summaryTaskId: String,
        val requestId: String,
        val filePath: String,
        val mediaBean: MediaBean?,
        val template: String,
        val transcribeContent: String,
        val token: String,
        val attempt: Int = 0
    )

    // 转写回调实现
    private val transcriptionCallback = object : TranscriptionPollingManager.TranscriptionCallback {
        override fun onTranscriptionStarted(taskId: String, filePath: String) {
            Timber.i("后台转写开始: taskId=$taskId, filePath=$filePath")

            // 处理从临时taskId到真实taskId的切换
            handleTaskIdUpdate(taskId, filePath)

            // 先更新数据库，等待完成后再通知监听者
            serviceScope.launch {
                // 等待数据库更新完成
                updateDatabaseOnTranscriptionStarted(taskId, filePath)

                // 数据库更新完成后，通知监听者
                notifyListeners { listener ->
                    listener.onTranscriptionStarted(taskId, filePath)
                }
            }
        }

        override fun onTranscriptionSuccess(taskId: String, filePath: String, phrases: List<Phrase>) {
            Timber.i("后台转写成功: taskId=$taskId, filePath=$filePath, phrases=${phrases.size}")

            // 先更新数据库，等待完成后再通知监听者
            serviceScope.launch {
                // 等待数据库更新完成
                updateDatabaseOnTranscriptionSuccess(taskId, filePath, phrases)

                // 检查是否需要自动总结
                val context = transcriptionContexts[taskId]
                if (context?.mediaBean != null && phrases.isNotEmpty()) {
                    checkAndStartAutoSummary(taskId, filePath, phrases, context)
                }

                // 移除已完成的任务
                activeTranscriptions.remove(taskId)
                transcriptionContexts.remove(taskId)

                // 数据库更新完成后，通知监听者
                notifyListeners { listener ->
                    listener.onTranscriptionSuccess(taskId, filePath, phrases)
                }
            }
        }

        override fun onTranscriptionFailed(taskId: String, filePath: String, errorCode: Int, errorMessage: String?) {
            Timber.e("后台转写失败: taskId=$taskId, filePath=$filePath, code=$errorCode, message=$errorMessage")

            // 先直接更新数据库，记录失败状态
            updateDatabaseOnTranscriptionFailed(taskId, filePath, errorCode, errorMessage)

            // 移除失败的任务
            activeTranscriptions.remove(taskId)
            transcriptionContexts.remove(taskId)

            // 然后通知监听者
            notifyListeners { listener ->
                listener.onTranscriptionFailed(taskId, filePath, errorCode, errorMessage)
            }
        }

        override fun onPollingStatusChanged(taskId: String, filePath: String, isPolling: Boolean) {
            Timber.i("后台轮询状态变化: taskId=$taskId, filePath=$filePath, isPolling=$isPolling")

            notifyListeners { listener ->
                listener.onPollingStatusChanged(taskId, filePath, isPolling)
            }
        }

        override fun onUploadStarted(taskId: String, filePath: String) {
            Timber.i("后台上传开始: taskId=$taskId, filePath=$filePath")
            // 通知监听者上传开始
            notifyListeners { listener ->
                listener.onUploadStarted(taskId, filePath)
            }
        }

        override fun onUploadSuccess(taskId: String, filePath: String) {
            Timber.i("后台上传成功: taskId=$taskId, filePath=$filePath")
            // 通知监听者上传成功
            notifyListeners { listener ->
                listener.onUploadSuccess(taskId, filePath)
            }
        }

        override fun onUploadFailed(taskId: String, filePath: String, errorCode: Int, errorMessage: String?) {
            Timber.e("后台上传失败: taskId=$taskId, filePath=$filePath, errorCode=$errorCode, errorMessage=$errorMessage")
            // 通知监听者上传失败
            notifyListeners { listener ->
                listener.onUploadFailed(taskId, filePath, errorCode, errorMessage)
            }
        }
    }

    init {
        // 初始化AI能力
        pollingManager.initAiCapability()
        Timber.i("后台转写服务初始化完成")
    }

    /**
     * 注册转写状态监听者
     */
    fun registerListener(listener: TranscriptionStateListener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener)
            Timber.i("注册转写监听者: ${listener.javaClass.simpleName}")
        }
    }

    /**
     * 取消注册转写状态监听者
     */
    fun unregisterListener(listener: TranscriptionStateListener) {
        listeners.remove(listener)
        Timber.i("取消注册转写监听者: ${listener.javaClass.simpleName}")
    }

    /**
     * 开始转写请求（完整版本，包含数据库更新所需参数）
     */
    fun startTranscription(
        request: TranscriptionPollingManager.TranscriptionRequest,
        mediaBean: MediaBean?,
        summaryTemplate: String
    ): String {
        Timber.i("开始后台转写: filePath=${request.filePath}")

        val taskId = pollingManager.startTranscription(request, transcriptionCallback)
        activeTranscriptions[taskId] = request.filePath

        // 存储转写上下文信息，用于后续数据库更新
        val context = TranscriptionContext(
            taskId = taskId,
            filePath = request.filePath,
            mediaBean = mediaBean,
            summaryTemplate = summaryTemplate,
            distinguishSpeakers = request.distinguishSpeakers,
            isReTranscribe = request.isReTranscribe,
            language = request.language
        )
        transcriptionContexts[taskId] = context

        Timber.i("后台转写任务已创建: taskId=$taskId, filePath=${request.filePath}")
        return taskId
    }

    /**
     * 使用已有taskId开始轮询（完整版本）
     */
    fun startPollingWithTaskId(taskId: String, filePath: String, mediaBean: MediaBean?, summaryTemplate: String) {
        activeTranscriptions[taskId] = filePath

        // 存储转写上下文信息（使用默认值，因为轮询时可能缺少部分信息）
        val context = TranscriptionContext(
            taskId = taskId,
            filePath = filePath,
            mediaBean = mediaBean,
            summaryTemplate = summaryTemplate,
            distinguishSpeakers = false, // 默认值
            isReTranscribe = false, // 默认值
            language = "zh-CN" // 默认值
        )
        transcriptionContexts[taskId] = context

        Timber.i("开始后台轮询: taskId=$taskId, filePath=$filePath")
        pollingManager.startPollingWithTaskId(taskId, filePath, transcriptionCallback)
    }

    /**
     * 处理文件上传中的情况（完整版本）
     */
    fun handleUploadingFile(filePath: String, mediaBean: MediaBean?, summaryTemplate: String): String {
        Timber.i("处理上传中文件: filePath=$filePath")

        val taskId = pollingManager.handleUploadingFile(filePath, transcriptionCallback)
        activeTranscriptions[taskId] = filePath

        // 存储转写上下文信息（使用默认值，因为上传时可能缺少部分信息）
        val context = TranscriptionContext(
            taskId = taskId,
            filePath = filePath,
            mediaBean = mediaBean,
            summaryTemplate = summaryTemplate,
            distinguishSpeakers = false, // 默认值
            isReTranscribe = false, // 默认值
            language = "zh-CN" // 默认值
        )
        transcriptionContexts[taskId] = context

        Timber.i("上传中文件处理任务已创建: taskId=$taskId, filePath=$filePath")
        return taskId
    }

    /**
     * 停止指定的转写任务
     */
    fun stopTranscription(taskId: String) {
        activeTranscriptions.remove(taskId)
        pollingManager.stopPolling()
        Timber.i("停止后台转写: taskId=$taskId")
    }

    /**
     * 检查是否有正在进行的转写任务
     */
    fun hasActiveTranscriptions(): Boolean {
        return activeTranscriptions.isNotEmpty()
    }

    /**
     * 获取当前活跃的转写任务
     */
    fun getActiveTranscriptions(): Map<String, String> {
        return activeTranscriptions.toMap()
    }

    /**
     * 检查指定文件是否正在转写
     */
    fun isTranscribing(filePath: String): Boolean {
        return activeTranscriptions.containsValue(filePath)
    }

    /**
     * 检查指定任务是否正在转写
     */
    fun isTaskTranscribing(taskId: String): Boolean {
        return activeTranscriptions.containsKey(taskId)
    }

    /**
     * 获取活跃转写任务的数量
     */
    fun getActiveTranscriptionCount(): Int {
        return activeTranscriptions.size
    }

    /**
     * 获取指定文件的转写任务ID
     */
    fun getTaskIdByFilePath(filePath: String): String? {
        return activeTranscriptions.entries.find { it.value == filePath }?.key
    }

    /**
     * 判断taskId是否为临时ID
     */
    fun isFakeTaskId(taskId: String): Boolean {
        return pollingManager.isFakeTaskId(taskId)
    }

    /**
     * 初始化AI能力（已在init中调用，此方法用于兼容性）
     */
    fun initAiCapability() {
        pollingManager.initAiCapability()
    }

    /**
     * 恢复未完成的转写任务
     * 在应用启动时调用，恢复之前未完成的转写轮询
     */
    suspend fun recoverUncompletedTranscriptions() {
        Timber.i("开始恢复未完成的转写任务")

        try {
            val uncompletedTasks = AudioTranscriptionDbHelper.getUncompletedTranscriptionTasks()

            if (uncompletedTasks.isEmpty()) {
                Timber.i("没有未完成的转写任务需要恢复")
                return
            }

            Timber.i("发现 ${uncompletedTasks.size} 个未完成的转写任务，开始恢复")

            uncompletedTasks.forEach { task ->
                val transcriptionId = task.transcriptionId
                if (transcriptionId.isNullOrEmpty()) {
                    Timber.w("跳过无效的转写任务: path=${task.path}")
                    return@forEach
                }

                // 获取对应的MediaBean
                val mediaBean = AudioTranscriptionDbHelper.getMediaBeanByTranscriptionTask(task)
                if (mediaBean == null) {
                    Timber.w("跳过无对应MediaBean的转写任务: taskId=$transcriptionId, path=${task.path}")
                    return@forEach
                }

                // 检查是否已经在轮询中
                if (isTaskTranscribing(transcriptionId)) {
                    Timber.w("任务已在轮询中，跳过: taskId=$transcriptionId")
                    return@forEach
                }

                // 恢复轮询
                val summaryTemplate = task.summaryTemplate ?: ""
                Timber.i("恢复转写任务轮询: taskId=$transcriptionId, path=${task.path}")

                if (isFakeTaskId(transcriptionId)) {
                    // 如果是临时taskId，检查文件是否还在上传中
                    val isUploading = AiCapabilityWrapper.uploadingFileMap[task.path]
                    if (isUploading == true) {
                        handleUploadingFile(task.path, mediaBean, summaryTemplate)
                    } else {
                        // 文件已上传完成但没有真实taskId，可能需要重新发起转写请求
                        Timber.w("临时taskId但文件未在上传中，可能需要重新转写: taskId=$transcriptionId, path=${task.path}")
                    }
                } else {
                    // 真实taskId，直接开始轮询
                    startPollingWithTaskId(transcriptionId, task.path, mediaBean, summaryTemplate)
                }
            }

            Timber.i("转写任务恢复完成，当前活跃任务数: ${getActiveTranscriptionCount()}")
        } catch (e: Exception) {
            Timber.e(e, "恢复未完成转写任务时发生错误")
        }
    }

    /**
     * 通知所有监听者
     */
    private fun notifyListeners(action: (TranscriptionStateListener) -> Unit) {
        serviceScope.launch(Dispatchers.Main) {
            listeners.forEach { listener ->
                try {
                    action(listener)
                } catch (e: Exception) {
                    Timber.e(e, "通知监听者时发生错误: ${listener.javaClass.simpleName}")
                }
            }
        }
    }

    /**
     * 销毁服务，释放资源
     */
    fun destroy() {
        Timber.i("销毁后台转写服务")
        listeners.clear()
        activeTranscriptions.clear()
        transcriptionContexts.clear()
        activeSummaries.clear()
        summaryContexts.clear()
        pollingManager.destroy()
        serviceScope.cancel()
    }

    // ========== 数据库更新方法 ==========

    /**
     * 处理从临时taskId到真实taskId的切换
     */
    private fun handleTaskIdUpdate(newTaskId: String, filePath: String) {
        // 查找是否有对应文件路径的临时taskId上下文
        val tempTaskIdEntry = transcriptionContexts.entries.find { (taskId, context) ->
            context.filePath == filePath && pollingManager.isFakeTaskId(taskId)
        }

        if (tempTaskIdEntry != null) {
            val (tempTaskId, context) = tempTaskIdEntry
            Timber.i("更新taskId映射: $tempTaskId -> $newTaskId, filePath=$filePath")

            // 移除临时taskId的上下文
            transcriptionContexts.remove(tempTaskId)

            // 使用新taskId创建更新的上下文
            val updatedContext = context.copy(taskId = newTaskId)
            transcriptionContexts[newTaskId] = updatedContext

            // 同时更新activeTranscriptions
            activeTranscriptions.remove(tempTaskId)
            activeTranscriptions[newTaskId] = filePath
        } else {
            Timber.w("未找到对应的临时taskId上下文: newTaskId=$newTaskId, filePath=$filePath")
        }
    }

    /**
     * 转写开始时更新数据库
     */
    private suspend fun updateDatabaseOnTranscriptionStarted(taskId: String, filePath: String) {
        val context = transcriptionContexts[taskId]
        Timber.i("转写开始数据库更新: taskId=$taskId, context存在=${context != null}, mediaBean存在=${context?.mediaBean != null}")

        if (context?.mediaBean != null) {
            withContext(Dispatchers.IO) {
                try {
                    Timber.i("后台服务更新数据库 - 转写开始: taskId=$taskId, mediaBean.path=${context.mediaBean.path}")
                    AudioTranscriptionDbHelper.updateTaskId(
                        context.mediaBean,
                        taskId,
                        context.summaryTemplate
                    )
                    Timber.i("后台服务成功更新转写开始状态到数据库: taskId=$taskId")
                } catch (e: Exception) {
                    Timber.e(e, "更新转写开始状态到数据库失败: taskId=$taskId")
                }
            }
        } else {
            Timber.w("无法更新数据库 - 缺少MediaBean: taskId=$taskId, filePath=$filePath, 当前上下文数量=${transcriptionContexts.size}")
            // 打印所有上下文信息用于调试
            transcriptionContexts.forEach { (id, ctx) ->
                Timber.d("上下文调试: taskId=$id, filePath=${ctx.filePath}, mediaBean存在=${ctx.mediaBean != null}")
            }
        }
    }

    /**
     * 转写成功时更新数据库
     */
    private suspend fun updateDatabaseOnTranscriptionSuccess(taskId: String, filePath: String, phrases: List<Phrase>) {
        val context = transcriptionContexts[taskId]
        Timber.i("转写成功数据库更新: taskId=$taskId, context存在=${context != null}, mediaBean存在=${context?.mediaBean != null}, phrases=${phrases.size}")

        if (context?.mediaBean != null && phrases.isNotEmpty()) {
            withContext(Dispatchers.IO) {
                try {
                    Timber.i("后台服务更新数据库 - 转写成功: taskId=$taskId, phrases=${phrases.size}, mediaBean.path=${context.mediaBean.path}")

                    // 准备转写内容数据
                    val contents = mutableListOf<String>()
                    val contentSpeaker = mutableListOf<Int>()
                    phrases.forEach { phrase ->
                        contents.add(com.google.gson.Gson().toJson(phrase))
                        contentSpeaker.add(phrase.speaker)
                    }

                    // 更新转写内容到数据库
                    AudioTranscriptionDbHelper.updateContent(
                        context.mediaBean,
                        context.distinguishSpeakers,
                        contents,
                        contentSpeaker
                    )

                    Timber.i("后台服务成功更新转写内容到数据库: taskId=$taskId, 内容条数=${contents.size}")
                } catch (e: Exception) {
                    Timber.e(e, "更新转写成功结果到数据库失败: taskId=$taskId")
                }
            }
        } else {
            Timber.w("无法更新数据库 - 缺少MediaBean或转写结果为空: taskId=$taskId, filePath=$filePath, phrases=${phrases.size}, 当前上下文数量=${transcriptionContexts.size}")
            // 打印所有上下文信息用于调试
            transcriptionContexts.forEach { (id, ctx) ->
                Timber.d("上下文调试: taskId=$id, filePath=${ctx.filePath}, mediaBean存在=${ctx.mediaBean != null}")
            }
        }
    }

    /**
     * 转写失败时更新数据库
     */
    private fun updateDatabaseOnTranscriptionFailed(taskId: String, filePath: String, errorCode: Int, errorMessage: String?) {
        val context = transcriptionContexts[taskId]
        if (context?.mediaBean != null) {
            serviceScope.launch(Dispatchers.IO) {
                try {
                    Timber.i("后台服务更新数据库 - 转写失败: taskId=$taskId, errorCode=$errorCode")

                    // 这里可以根据需要添加失败状态的数据库更新逻辑
                    // 目前AudioTranscriptionDbHelper没有专门的失败状态更新方法
                    // 可以考虑清空转写内容或设置错误标志

                    Timber.i("后台服务记录转写失败状态: taskId=$taskId, errorCode=$errorCode")
                } catch (e: Exception) {
                    Timber.e(e, "更新转写失败状态到数据库失败: taskId=$taskId")
                }
            }
        } else {
            Timber.w("无法更新数据库 - 缺少MediaBean: taskId=$taskId, filePath=$filePath")
        }
    }

    // ========== 录音总结相关方法 ==========

    /**
     * 检查并开始自动总结
     */
    private suspend fun checkAndStartAutoSummary(
        taskId: String,
        filePath: String,
        phrases: List<Phrase>,
        context: TranscriptionContext
    ) {
        try {
            // 确保在IO线程中执行数据库查询，避免竞争条件
            val audioBean = withContext(Dispatchers.IO) {
                AudioTranscriptionDbHelper.findCorrespondBean(context.mediaBean!!).firstOrNull()
            }

            val shouldAutoSummary = audioBean?.summaryTaskId.isNullOrEmpty()
            Timber.i("检查是否需要自动总结: taskId=$taskId, shouldAutoSummary=$shouldAutoSummary, summaryTaskId=${audioBean?.summaryTaskId}")

            if (shouldAutoSummary && context.summaryTemplate.isNotEmpty()) {
                // 准备转写内容
                val transcribeContent = phrases.joinToString("") { phrase ->
                    phrase.text
                }

                if (transcribeContent.isNotEmpty()) {
                    Timber.i("开始自动总结: taskId=$taskId, template=${context.summaryTemplate}, content.length=${transcribeContent.length}")
                    // 直接调用suspend函数，不需要额外的协程启动
                    context.mediaBean?.let { mediaBean ->
                        startSummaryRequestSuspend(taskId, filePath, mediaBean, context.summaryTemplate, transcribeContent)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "检查自动总结时发生错误: taskId=$taskId")
        }
    }

    /**
     * 开始总结请求（suspend版本，用于内部调用）
     */
    private suspend fun startSummaryRequestSuspend(
        transcriptionTaskId: String,
        filePath: String,
        mediaBean: MediaBean,
        template: String,
        transcribeContent: String
    ) {
        try {
            // 获取AI Token（使用suspendCancellableCoroutine包装回调）
            val token = suspendCancellableCoroutine<String> { continuation ->
                AiCapabilityWrapper.INSTANCE.getToken { token ->
                    continuation.resume(token)
                }
            }

            // 直接调用实际的总结请求
            realRequestSummary(transcriptionTaskId, filePath, mediaBean, template, transcribeContent, token)
        } catch (e: Exception) {
            Timber.e(e, "开始总结请求时发生错误: transcriptionTaskId=$transcriptionTaskId")
            notifyListeners { listener ->
                listener.onSummaryFailed(transcriptionTaskId, filePath, "", REQUEST_SUMMARY_ERROR, template)
            }
        }
    }

    /**
     * 实际执行总结请求
     */
    private suspend fun realRequestSummary(
        transcriptionTaskId: String,
        filePath: String,
        mediaBean: MediaBean,
        template: String,
        transcribeContent: String,
        token: String
    ) {
        try {
            Timber.i("实际执行总结请求: transcriptionTaskId=$transcriptionTaskId, content.length=${transcribeContent.length}")

            val requestId = UUID.randomUUID().toString()

            summaryRepository.createSummaryTask(
                template,
                requestId,
                transcribeContent,
                token
            ).collect { result ->
                when {
                    result.isSuccess() -> {
                        Timber.i("总结请求成功: ${result.data}")
                        result.data?.taskId?.let { summaryTaskId ->
                            // 更新数据库中的总结任务ID
                            withContext(Dispatchers.IO) {
                                AudioTranscriptionDbHelper.updateSummaryTaskId(mediaBean, summaryTaskId)
                            }

                            // 创建总结上下文
                            val summaryContext = SummaryContext(
                                transcriptionTaskId = transcriptionTaskId,
                                summaryTaskId = summaryTaskId,
                                requestId = requestId,
                                filePath = filePath,
                                mediaBean = mediaBean,
                                template = template,
                                transcribeContent = transcribeContent,
                                token = token
                            )

                            summaryContexts[summaryTaskId] = summaryContext
                            activeSummaries[summaryTaskId] = transcriptionTaskId

                            // 通知监听者总结开始
                            notifyListeners { listener ->
                                listener.onSummaryStarted(transcriptionTaskId, filePath, summaryTaskId, template)
                            }

                            // 开始轮询总结结果
                            startSummaryPolling(summaryTaskId)
                        }
                    }
                    result.isLoading() -> {
                        Timber.i("总结请求加载中")
                    }
                    result.isError() -> {
                        Timber.e("总结请求失败: code=${result.code}, message=${result.message}")
                        handleSummaryFailed(transcriptionTaskId, filePath, "", result.code ?: REQUEST_SUMMARY_ERROR, template)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "执行总结请求时发生异常: transcriptionTaskId=$transcriptionTaskId")
            handleSummaryFailed(transcriptionTaskId, filePath, "", REQUEST_SUMMARY_ERROR, template)
        }
    }

    /**
     * 开始总结轮询
     */
    private fun startSummaryPolling(summaryTaskId: String) {
        serviceScope.launch {
            pollSummaryResult(summaryTaskId, 0)
        }
    }

    /**
     * 轮询总结结果
     */
    private suspend fun pollSummaryResult(summaryTaskId: String, attempt: Int) {
        val context = summaryContexts[summaryTaskId]
        if (context == null) {
            Timber.w("总结上下文不存在，停止轮询: summaryTaskId=$summaryTaskId")
            return
        }

        if (attempt >= MAX_RETRY_ATTEMPTS) {
            Timber.e("总结轮询达到最大重试次数: summaryTaskId=$summaryTaskId")
            handleSummaryFailed(context.transcriptionTaskId, context.filePath, summaryTaskId, GET_SUMMARY_RESULT_ERROR, context.template)
            return
        }

        try {
            summaryRepository.getSummaryResult(context.requestId, summaryTaskId, context.token).collect { result ->
                when {
                    result.isSuccess() -> {
                        Timber.i("总结轮询成功: summaryTaskId=$summaryTaskId, data=${result.data != null}")
                        result.data?.let { response ->
                            val title = response.title?.trim() ?: ""
                            val content = response.content ?: ""

                            // 更新数据库
                            withContext(Dispatchers.IO) {
                                AudioTranscriptionDbHelper.updateSummaryContent(
                                    context.mediaBean!!,
                                    content,
                                    summaryTitle = title,
                                    template = context.template,
                                    summaryErrorCode = 0
                                )
                            }

                            // 清理上下文
                            summaryContexts.remove(summaryTaskId)
                            activeSummaries.remove(summaryTaskId)

                            // 通知监听者总结成功
                            notifyListeners { listener ->
                                listener.onSummarySuccess(context.transcriptionTaskId, context.filePath, summaryTaskId, title, content, context.template)
                            }
                        }
                    }
                    result.isLoading() -> {
                        Timber.i("总结还在处理中，继续轮询: summaryTaskId=$summaryTaskId, attempt=$attempt")
                        // 延迟后继续轮询
                        delay(POLLING_DELAY_MS)
                        pollSummaryResult(summaryTaskId, attempt + 1)
                    }
                    result.isError() -> {
                        Timber.e("总结轮询失败: summaryTaskId=$summaryTaskId, code=${result.code}, message=${result.message}")
                        handleSummaryFailed(context.transcriptionTaskId, context.filePath, summaryTaskId, result.code ?: GET_SUMMARY_RESULT_ERROR, context.template)
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "总结轮询异常: summaryTaskId=$summaryTaskId, attempt=$attempt")
            // 延迟后重试
            delay(POLLING_DELAY_MS)
            pollSummaryResult(summaryTaskId, attempt + 1)
        }
    }

    /**
     * 处理总结失败
     */
    private suspend fun handleSummaryFailed(
        transcriptionTaskId: String,
        filePath: String,
        summaryTaskId: String,
        errorCode: Int,
        template: String
    ) {
        try {
            val context = summaryContexts[summaryTaskId]
            if (context?.mediaBean != null) {
                // 更新数据库记录失败状态
                withContext(Dispatchers.IO) {
                    AudioTranscriptionDbHelper.updateSummaryContent(
                        context.mediaBean,
                        "",
                        summaryTitle = "",
                        template = template,
                        summaryErrorCode = errorCode
                    )
                }
            }

            // 清理上下文
            summaryContexts.remove(summaryTaskId)
            activeSummaries.remove(summaryTaskId)

            // 通知监听者总结失败
            notifyListeners { listener ->
                listener.onSummaryFailed(transcriptionTaskId, filePath, summaryTaskId, errorCode, template)
            }
        } catch (e: Exception) {
            Timber.e(e, "处理总结失败时发生异常: summaryTaskId=$summaryTaskId")
        }
    }

    /**
     * 手动请求总结（用于重新总结）
     */
    fun requestSummary(
        transcriptionTaskId: String,
        filePath: String,
        mediaBean: MediaBean,
        template: String,
        transcribeContent: String,
        isReSummary: Boolean = false
    ) {
        Timber.i("手动请求总结: transcriptionTaskId=$transcriptionTaskId, isReSummary=$isReSummary")

        serviceScope.launch {
            try {
                if (isReSummary) {
                    // 清空现有总结内容
                    withContext(Dispatchers.IO) {
                        AudioTranscriptionDbHelper.clearSummary(mediaBean, template)
                    }
                }

                // 直接调用suspend版本，避免重复的协程启动
                startSummaryRequestSuspend(transcriptionTaskId, filePath, mediaBean, template, transcribeContent)
            } catch (e: Exception) {
                Timber.e(e, "手动请求总结时发生错误: transcriptionTaskId=$transcriptionTaskId")
                notifyListeners { listener ->
                    listener.onSummaryFailed(transcriptionTaskId, filePath, "", REQUEST_SUMMARY_ERROR, template)
                }
            }
        }
    }

    /**
     * 检查是否有正在进行的总结任务
     */
    fun hasActiveSummaries(): Boolean {
        return activeSummaries.isNotEmpty()
    }

    /**
     * 获取当前活跃的总结任务
     */
    fun getActiveSummaries(): Map<String, String> {
        return activeSummaries.toMap()
    }

    /**
     * 检查指定转写任务是否正在总结
     */
    fun isSummarizing(transcriptionTaskId: String): Boolean {
        return activeSummaries.containsValue(transcriptionTaskId)
    }

    /**
     * 检查指定文件是否正在总结
     * @param filePath 音频文件路径
     * @return 是否正在总结
     */
    fun isSummarizing(filePath: String): Boolean {
        return summaryContexts.values.any { it.filePath == filePath }
    }

    /**
     * 停止指定的总结任务
     */
    fun stopSummary(summaryTaskId: String) {
        summaryContexts.remove(summaryTaskId)
        activeSummaries.remove(summaryTaskId)
        Timber.i("停止总结任务: summaryTaskId=$summaryTaskId")
    }
}
